import "./index.css";
import { createRoot } from "react-dom/client";
import { Auth0Provider } from "@auth0/auth0-react";
import App from "./App.tsx";

createRoot(document.getElementById("root")!).render(
  <Auth0Provider
    domain="novel-archives.eu.auth0.com"
    clientId="Yia6Czz5yFkzgJzyBJKF25bMGc5RocU9"
    authorizationParams={{
      audience: "https://novel-archives.eu.auth0.com/api/v2/",
      redirect_uri: window.location.origin,
    }}
  >
    <App />
  </Auth0Provider>
);
