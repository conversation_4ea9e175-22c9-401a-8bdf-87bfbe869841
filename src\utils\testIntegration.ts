// Test utility to verify the integration is working correctly
export const testIntegration = {
  // Test environment variables
  checkEnvironment: () => {
    const required = [
      'VITE_AUTH0_DOMAIN',
      'VITE_AUTH0_CLIENT_ID',
      'VITE_AUTH0_AUDIENCE',
      'VITE_API_URL'
    ];

    const missing = required.filter(key => !import.meta.env[key]);

    console.log('🔍 Environment Variables Check:');
    required.forEach(key => {
      const value = import.meta.env[key];
      if (value) {
        console.log(`✅ ${key}: ${value}`);
      } else {
        console.log(`❌ ${key}: NOT SET`);
      }
    });

    if (missing.length > 0) {
      console.error('❌ Missing environment variables:', missing);
      console.log('💡 Create a .env file with the required variables');
      return false;
    }

    // Check for common Auth0 configuration issues
    const audience = import.meta.env.VITE_AUTH0_AUDIENCE;
    const apiUrl = import.meta.env.VITE_API_URL;

    if (audience && audience.includes('.workers.dev')) {
      console.warn('⚠️  WARNING: Auth0 audience should NOT be your worker URL');
      console.log('💡 Use a logical identifier like: https://novel-archives-api');
    }

    if (apiUrl && !apiUrl.includes('.workers.dev')) {
      console.warn('⚠️  WARNING: API URL should be your actual worker domain');
      console.log('💡 Use your actual worker URL like: https://your-worker.workers.dev');
    }

    console.log('✅ All environment variables are set');
    return true;
  },

  // Test API connectivity (without auth)
  checkApiConnectivity: async () => {
    const apiUrl = import.meta.env.VITE_API_URL;
    
    try {
      const response = await fetch(`${apiUrl}/api/series/statuses`);
      
      if (response.status === 401) {
        console.log('✅ API is reachable (authentication required as expected)');
        return true;
      }
      
      if (response.ok) {
        console.log('✅ API is reachable and responding');
        return true;
      }
      
      console.error('❌ API returned error:', response.status);
      return false;
    } catch (error) {
      console.error('❌ Cannot reach API:', error);
      return false;
    }
  },

  // Run all tests
  runAll: async () => {
    console.log('🧪 Running integration tests...\n');
    
    const envTest = testIntegration.checkEnvironment();
    const apiTest = await testIntegration.checkApiConnectivity();
    
    console.log('\n📊 Test Results:');
    console.log(`Environment: ${envTest ? '✅' : '❌'}`);
    console.log(`API Connectivity: ${apiTest ? '✅' : '❌'}`);
    
    if (envTest && apiTest) {
      console.log('\n🎉 Integration tests passed! You can proceed with authentication.');
    } else {
      console.log('\n⚠️  Some tests failed. Please check your configuration.');
    }
    
    return envTest && apiTest;
  }
};

// Auto-run tests in development
if (import.meta.env.DEV) {
  // Run tests after a short delay to ensure environment is loaded
  setTimeout(() => {
    testIntegration.runAll();
  }, 1000);
}
