# Auth0 Configuration Fix Guide

## 🚨 Current Issue

You're getting this error:
```
access_denied&error_description=Service not found: https://novel-archives-api.a-borgaiskender.workers.dev/
```

This happens because Auth0 is trying to validate your API audience URL, but the URL doesn't exist.

## 🔧 How to Fix

### Step 1: Fix Auth0 API Configuration

1. **Go to Auth0 Dashboard → APIs**
2. **Find your Novel Archives API**
3. **Check the "Identifier" field**

**Current (WRONG)**: `https://novel-archives-api.a-borgaiskender.workers.dev/`
**Should be (CORRECT)**: `https://novel-archives-api`

### Step 2: Update Your Environment Variables

Create/update your `.env` file:

```bash
# Auth0 Configuration
VITE_AUTH0_DOMAIN=novel-archives.uk.auth0.com
VITE_AUTH0_CLIENT_ID=your-actual-client-id
VITE_AUTH0_AUDIENCE=https://novel-archives-api

# API Configuration - Your actual worker domain
VITE_API_URL=https://your-actual-worker.workers.dev
```

### Step 3: Key Differences to Understand

| Setting | Purpose | Example |
|---------|---------|---------|
| **Auth0 API Audience** | Logical identifier for your API | `https://novel-archives-api` |
| **API Base URL** | Actual URL where your API is hosted | `https://your-worker.workers.dev` |

**Important**: These are DIFFERENT values!

## 🛠️ Complete Auth0 Setup

### 1. Auth0 API Configuration

```
Name: Novel Archives API
Identifier: https://novel-archives-api
Signing Algorithm: RS256
```

**Scopes to add**:
- `read:series` - Read series data
- `write:series` - Create, update, delete series

### 2. Auth0 Application Configuration

```
Name: Novel Archives Frontend
Type: Single Page Application
```

**URLs to configure** (replace with your actual domains):
```
Allowed Callback URLs: 
  http://localhost:5173, 
  https://your-frontend.pages.dev

Allowed Logout URLs: 
  http://localhost:5173, 
  https://your-frontend.pages.dev

Allowed Web Origins: 
  http://localhost:5173, 
  https://your-frontend.pages.dev
```

## 🔍 Debugging Steps

### 1. Verify Your Worker Domain

First, find your actual Cloudflare Workers domain:

1. Go to Cloudflare Dashboard → Workers & Pages
2. Find your worker
3. Copy the actual domain (e.g., `https://novel-archives.your-subdomain.workers.dev`)

### 2. Test Your API Directly

```bash
# Test if your API is accessible
curl https://your-actual-worker.workers.dev/api/series/statuses

# Should return 401 (authentication required) or the statuses array
```

### 3. Check Auth0 Configuration

In Auth0 Dashboard:
1. **APIs → Your API → Settings**: Verify identifier is `https://novel-archives-api`
2. **Applications → Your App → Settings**: Verify callback URLs include your domain
3. **APIs → Your API → Scopes**: Verify `read:series` and `write:series` exist

## 🚀 Updated Environment Configuration

Based on your new backend prompt, update your `.env`:

```bash
# Auth0 Configuration
VITE_AUTH0_DOMAIN=novel-archives.uk.auth0.com
VITE_AUTH0_CLIENT_ID=your-auth0-client-id
VITE_AUTH0_AUDIENCE=https://novel-archives-api

# API Configuration - Your actual Cloudflare Workers domain
VITE_API_URL=https://your-actual-worker.workers.dev

# For Cloudflare Pages deployment, also set these in your Pages environment variables
```

## 🔄 Testing the Fix

### 1. Clear Browser Data
```bash
# Clear localStorage and cookies for your domain
# Or use incognito/private browsing
```

### 2. Restart Development Server
```bash
npm start
```

### 3. Test Authentication Flow
1. Visit your app
2. Click login
3. Complete Auth0 flow
4. Should redirect back successfully

### 4. Test API Calls
1. Try adding a series
2. Check browser network tab for API calls
3. Verify JWT token is included in requests

## 🚨 Common Mistakes to Avoid

1. **Don't use your worker URL as Auth0 audience**
   - ❌ `https://your-worker.workers.dev`
   - ✅ `https://novel-archives-api`

2. **Don't include trailing slashes**
   - ❌ `https://novel-archives-api/`
   - ✅ `https://novel-archives-api`

3. **Don't mix up the URLs**
   - Auth0 Audience: Logical identifier
   - API Base URL: Actual worker domain

## 📞 If Still Having Issues

1. **Check Auth0 Logs**:
   - Go to Auth0 Dashboard → Monitoring → Logs
   - Look for failed authentication attempts

2. **Check Browser Console**:
   - Look for specific error messages
   - Check network tab for failed requests

3. **Verify Worker is Running**:
   - Test your worker URL directly in browser
   - Should return some response (even if 401)

4. **Double-check Environment Variables**:
   - Restart dev server after changing `.env`
   - Verify variables are loaded: `console.log(import.meta.env)`

The key fix is ensuring your Auth0 API identifier is a logical name (`https://novel-archives-api`) and NOT your actual worker URL.
